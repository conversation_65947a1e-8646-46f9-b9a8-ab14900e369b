package com.concirrus.referencedataservice.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


@ToString
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PagedResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 7789284253047806745L;
    private Object data;
    private boolean success = true;
    private String message = SUCCESS_MESSAGE;
    private static final String SUCCESS_MESSAGE = "Request Processed Successfully";
    @JsonFormat(
            pattern = "yyyy-MM-dd'T'HH:mm:ssZ"
    )
    private Date timestamp = new Date();

    private Long totalItems;

    public void setMessage(String message) {
        if (StringUtils.isNotBlank(message)) {
            message = message.replaceAll("(\\w+\\.+)", "");
        }

        this.message = message;
    }
}
