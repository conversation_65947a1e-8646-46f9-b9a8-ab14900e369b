package com.concirrus.referencedataservice.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RequestConstant {
    public static final String CLIENT_ID = "client-id";
    public static final String USER_ID = "x-user-id";
    public static final String IS_SUBMISSION_DISABLED = "isSubmissionDisabled";
    public static final String IS_ENDORSEMENT = "isEndorsement";
    public static final String USER_ROLES = "x-user-roles";
    public static final String AUTHORISATION_USER_ROLES = "user-roles";
    public static final int PAGE_SIZE_MIN = 1;
    public static final int PAGE_SIZE_MAX = 100;
    public static final int PAGE_NUMBER_MIN = 0;
    public static final int STATUS_PAGE_SIZE_MAX = 1000;
    public static final String ZERO = "0";
    public static final String TEN = "10";
    public static final String TWENTY = "20";
    public static final String HUNDRED = "100";
    public static final String THOUSAND = "1000";
    public static final String HYPHEN = "-";
    public static final String ASC = "ASC";
    public static final String DESC = "DESC";

    public static final String PAGE_NUMBER = "pageNumber";
    public static final String PAGE_SIZE = "pageSize";
    public static final String SORT_ORDER= "sortOrder";
    public static final String SORT_BY= "sortBy";
    public static final String LINE_OF_BUSINESS = "line-of-business";
    public static final String APPLIED_LOGISTICS = "APPLIED_LOGISTICS";
    public static final String REFERENCE_DATA = "reference-data";

}
