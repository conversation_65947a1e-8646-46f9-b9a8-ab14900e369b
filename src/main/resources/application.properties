spring.application.name=reference-data-service
server.port=8080
server.servlet.context-path=/reference-data-service

mongo.reference.db=${MONGO_REFERENCE_DB:test}
mongo.reference.uri=mongodb://${MONGO_USERNAME:username}:${MONGO_PASSWORD:password}@${MONGO_IPS:************:27017}/${MONGO_DEFAULT_DB:defaultauthdb}?authSource=admin&connectTimeoutMS=30000


#ACTUATOR
management.endpoints.web.base-path=/manage
management.endpoints.web.exposure.include=health, info, prometheus, restart
management.endpoint.info.enabled=true
management.endpoint.health.enabled=true
management.endpoint.health.show-details=always
management.endpoint.restart.enabled=true