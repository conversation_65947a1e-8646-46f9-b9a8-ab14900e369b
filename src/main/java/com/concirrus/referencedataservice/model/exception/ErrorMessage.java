package com.concirrus.referencedataservice.model.exception;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> mohit
 * @since : Jan 2024
 */
public class ErrorMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = -8320506714966460235L;
    private long timestamp;
    private int status;
    private String message;
    private String error;
    private String exception;
    private String path;

    public ErrorMessage() {
    }

    public ErrorMessage(long timestamp, int status, String message, String error, String exception, String path) {
        super();
        this.timestamp = timestamp;
        this.status = status;
        setMessage(message);
        setError(error);
        if (StringUtils.isNotBlank(exception) && exception.split("\\.").length > 1)
            exception = exception.split("\\.")[exception.split("\\.").length - 1];

        setException(exception);
        this.path = path;
    }

    public ErrorMessage(long timestamp, String message, String error) {
        super();
        this.timestamp = timestamp;
        setMessage(message);
        setError(error);
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        if (StringUtils.isNotBlank(message))
            message = message.replaceAll("(\\w+\\.+)", "");
        this.message = StringEscapeUtils.escapeHtml4(message);
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = StringEscapeUtils.escapeHtml4(error);
    }

    public String getException() {
        return exception;
    }

    public void setException(String exception) {
        if (StringUtils.isNotBlank(exception) && exception.split("\\.").length > 1)
            exception = exception.split("\\.")[exception.split("\\.").length - 1];

        this.exception = StringEscapeUtils.escapeHtml4(exception);
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    @Override
    public String toString() {
        return String.format("ErrorMessage [timestamp=%d, status=%d, message=%s, error=%s, exception=%s, path=%s]",
                timestamp, status, message, error, exception, path);
    }

}
