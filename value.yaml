############################### Number of Instance required for this services ########################
replicaCount: 2
############################### 1.Service container port is the port on which services listen, 2. Service port coulde be anything but #genrally should be 80 for all services 3. service container port and service target port should be Equal  ########################
service:
  container:
    port: 8080
  type: ClusterIP
  port: 80
  targetport: 8080
############## CPU and Memory required from this service requests=Minimum , limits=maximum resources######
resources: 
  limits:
    cpu: 300m
    memory: 500Mi
  requests:
    cpu: 100m
    memory: 110Mi

############## All service must implement health check status and the context path of the service should be added at livenessProbe.path #
livenessProbe:
  enabled: true
  path: /reference-data-service/manage/health
  initialDelaySeconds: 180
  periodSeconds: 20
  timeoutSeconds: 30
  successThreshold: 1
  failureThreshold: 3
############## All service must implement health check status and the context path of the service should be added at readinessProbe.path #

readinessProbe:
  enabled: true
  path: /reference-data-service/manage/health
  initialDelaySeconds: 180
  periodSeconds: 20
  timeoutSeconds: 30
  successThreshold: 1
  failureThreshold: 3
  
