package com.concirrus.referencedataservice.constant;


import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DBConstant {
    public static final String REFERENCE_MONGO_COLLECTION = "reference_data";
    public static final String COVERAGE_TYPE = "coverageType";
    public static final String POLICY_FORM = "policyForm";
    public static final String ENTITY_TYPE = "entityType";
    public static final String STATES_LIST = "states";
    public static final String ENDORSEMENT_DATA_QUERY_KEY = "data.";
    public static final String CREATED_AT = "createdAt";
    public static final String MONGO_CLIENT_ID = "clientId";
    public static final String MONGO_ID = "_id";
    public static final String UPDATED_AT = "updatedAt";
    public static final String UPDATED_BY = "updatedBy";
    public static final String CREATED_BY = "createdBy";
    public static final String ENDORSEMENT_TYPE = "endorsementType";

    public static final String MANDATORY = "Mandatory";
    public static final String OPTIONAL = "Optional";
    public static final String ENDORSEMENT = "ENDORSEMENT";

    public static final String FORM_NAME = "formName";
    public static final String ISSUING_COMPANY = "issuingCompany";
    public static final String CARRIER_TYPE = "carrierType";

    public static final String WAR_HULL_OPTIONAL = "isEndorsementWarHullOptional";
    public static final String WAR_LIABILITY_OPTIONAL = "isEndorsementWarLiabilityOptional";
    public static final String TRIA_HULL_OPTIONAL = "isEndorsementTriaHullOptional";
    public static final String TRIA_LIABILITY_OPTIONAL = "isEndorsementTriaLiabilityOptional";
    public static final String LEAD_POLICY_OPTIONAL = "isEndorsementLeadPolicyOptional";
    public static final String NAMED_INSURED_LLC_OPTIONAL = "isEndorsementNamedInsuredLLCOptional";
    public static final String DEFAULT_SELECTED = "defaultSelected";
}
