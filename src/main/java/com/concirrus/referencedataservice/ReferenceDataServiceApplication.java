package com.concirrus.referencedataservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

/**
 * The entry point of the Spring Boot application.
 * <p>
 * This class contains the main method which is the starting point of the
 * Spring Boot application. It initializes the application context and
 * starts the embedded web server.
 * </p>
 */
@SpringBootApplication
@EnableCaching
public class ReferenceDataServiceApplication {
	/**
	 * The main method that launches the Spring Boot application.
	 *
	 * @param args the command-line arguments passed during application startup
	 */
	public static void main(String[] args) {
		SpringApplication.run(ReferenceDataServiceApplication.class, args);
	}

}
