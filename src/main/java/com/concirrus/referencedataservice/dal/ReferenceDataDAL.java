package com.concirrus.referencedataservice.dal;

import com.concirrus.referencedataservice.dto.request.EndorsementSearchCriteria;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Map;

public interface ReferenceDataDAL {

    Pair<Long, List<Document>> getEndorsementDataBy(String endorsementNature, String endorsementType, EndorsementSearchCriteria searchCriteria, boolean includeCorporateEndorsements, PageRequest pageRequest, String clientId);

    Pair<Long, List<Document>> getEndorsementListDataBy(String endorsementNature, String carrierType, String state, PageRequest pageRequest, String clientId);

    Pair<Long, List<Document>> getFormsListDataBy(String formNature, List<String> classes, List<String> lineOfBusinesses, String state, PageRequest pageRequest, String clientId);

    Pair<Long, List<Document>> getReferenceData(Map<String, Object> filters, PageRequest pageRequest, String clientId);

    Document getRecordByEntityId(String entityId, String clientId);

    Document updateRecordByEntityId(String entityId, Document updatedData, String clientId);

    Document save(Document referenceDataEntity);

    void deleteRecordById(String entityId, String clientId);

}
