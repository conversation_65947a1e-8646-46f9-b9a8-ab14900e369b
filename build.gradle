plugins {
	id 'java'
	id 'org.springframework.boot' version '3.2.8'
	id 'io.spring.dependency-management' version '1.1.6'
	id "org.sonarqube" version "4.4.1.3373"
	id "jacoco"
}

jacoco {
	toolVersion = "0.8.10"
}

group = 'com.concirrus'
version = '0.0.1-SNAPSHOT'

jar {
	archivesBaseName = "reference-data-service"
	project.version = ""
}

java {
	sourceCompatibility = '17'
}

ext {
	set('springCloudGcpVersion', "5.4.3")
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	implementation 'javax.persistence:javax.persistence-api:2.2'
	compileOnly 'org.projectlombok:lombok:1.18.20'
	annotationProcessor 'org.projectlombok:lombok:1.18.20'
	implementation 'org.springframework.boot:spring-boot-starter-web'

	implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
	implementation 'org.springframework.boot:spring-boot-starter-cache'

	// <PERSON> Databind
	implementation 'com.fasterxml.jackson.core:jackson-databind'

	implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.14.0'

	// https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-aop
	implementation 'org.springframework.boot:spring-boot-starter-aop:3.3.0'

	implementation 'jakarta.validation:jakarta.validation-api:3.0.0'
	implementation 'org.springframework.boot:spring-boot-starter-validation:3.2.2'

	// https://mvnrepository.com/artifact/org.apache.commons/commons-text
	implementation 'org.apache.commons:commons-text:1.11.0'

	// kubernetes
	implementation 'org.springframework.cloud:spring-cloud-starter-kubernetes-client-config:2.1.9'
}

dependencyManagement {
	imports {
		mavenBom "com.google.cloud:spring-cloud-gcp-dependencies:${springCloudGcpVersion}"
	}
}

tasks.named('test') {
	useJUnitPlatform()
}


sonarqube {
	properties {
		property 'sonar.coverage.exclusions', "**/com/concirrus/referencedataservice/annotation/**," +
				"**/com/concirrus/referencedataservice/config/**," +
				"**/com/concirrus/referencedataservice/constant/**," +
				"**/com/concirrus/referencedataservice/dto/**," +
				"**/com/concirrus/referencedataservice/controller/**," +
				"**/com/concirrus/referencedataservice/enums/**," +
				"**/com/concirrus/referencedataservice/model/**," +
				"**/com/concirrus/referencedataservice/dal/**," +
				"**/com/concirrus/referencedataservice/ReferenceDataServiceApplication.java"
	}
}

jacocoTestReport {
	reports {
		html.required = true
		xml.required = true
		xml.destination file("${buildDir}/reports/jacoco.xml")
	}
}

plugins.withType(JacocoPlugin) {
	tasks["test"].finalizedBy 'jacocoTestReport'
}
