package com.concirrus.referencedataservice.controller;

import com.concirrus.referencedataservice.dto.request.EndorsementSearchCriteria;
import com.concirrus.referencedataservice.dto.request.FormSearchCriteria;
import com.concirrus.referencedataservice.dto.response.PagedResponse;
import com.concirrus.referencedataservice.service.ReferenceDataService;
import com.concirrus.referencedataservice.utils.LoggingUtils;
import jakarta.validation.constraints.Min;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.concirrus.referencedataservice.constant.DBConstant.CREATED_AT;
import static com.concirrus.referencedataservice.constant.RequestConstant.*;
import static com.concirrus.referencedataservice.constant.RequestConstant.APPLIED_LOGISTICS;

@Slf4j
@RestController
@RequestMapping("/forms")
public class FormsDataController {

    private final ReferenceDataService referenceDataService;

    public FormsDataController(ReferenceDataService referenceDataService) {
        this.referenceDataService = referenceDataService;
    }

    @GetMapping("/{formNature}")
    public PagedResponse getFormsBy(
            @PathVariable String formNature,
            @ModelAttribute FormSearchCriteria criteria,
            @RequestParam(defaultValue = ZERO) @Min(PAGE_NUMBER_MIN) int pageNumber,
            @RequestParam(defaultValue = TEN) @Min(PAGE_SIZE_MIN) int pageSize,
            @RequestParam(defaultValue = ASC) Sort.Direction sortOrder,
            @RequestParam(defaultValue = CREATED_AT) String sortBy,
            @RequestHeader(value = CLIENT_ID) String clientId) {
        log.info(LoggingUtils.logMethodEntry(formNature, criteria, pageNumber, pageSize, sortOrder, sortBy, clientId));
        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortOrder, sortBy));
        PagedResponse response = new PagedResponse();
        Pair<Long, List<Document>> data = referenceDataService.getFormsListBy(formNature, criteria, pageRequest, clientId);
        response.setData(data.getRight());
        response.setTotalItems(data.getLeft());
        return response;
    }
}
