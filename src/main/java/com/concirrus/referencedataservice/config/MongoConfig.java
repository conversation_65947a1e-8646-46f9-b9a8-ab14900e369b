package com.concirrus.referencedataservice.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;

/**
 * Configuration class for setting up MongoDB connections.
 * <p>
 * This class configures the {@link MongoTemplate} bean that is used to interact
 * with MongoDB. It defines the database name and connection URI to establish a
 * connection to the MongoDB instance.
 * </p>
 */
@Configuration
public class MongoConfig {

    private static final Logger logger = LoggerFactory.getLogger(MongoConfig.class);

    public static final String MONGO_TEMPLATE = "mongoTemplate";

    @Value("${mongo.reference.db}")
    private String database;

    @Value("${mongo.reference.uri}")
    private String uri;

    /**
     * Creates a {@link MongoTemplate} bean that can be used to perform MongoDB operations.
     *
     * @return the configured {@link MongoTemplate} instance.
     */
    @Bean(name = MONGO_TEMPLATE)
    @Primary
    public MongoTemplate mongoTemplate() {
        logger.info("Creating MongoTemplate bean with database: {}", database);
        return new MongoTemplate(getMongoFactory(uri, database));
    }

    /**
     * Creates a {@link MongoDatabaseFactory} to manage the MongoDB connection.
     *
     * @param uri the connection URI for MongoDB.
     * @param dbName the name of the MongoDB database to connect to.
     * @return the {@link MongoDatabaseFactory} instance for the specified URI and database.
     */
    public static MongoDatabaseFactory getMongoFactory(String uri, String dbName) {
        logger.info("Creating MongoDatabaseFactory with URI: {} and Database: {}", uri, dbName);
        MongoClient mongoClient = MongoClients.create(uri);
        MongoDatabaseFactory factory = new SimpleMongoClientDatabaseFactory(mongoClient, dbName);
        logger.info("MongoDatabaseFactory created successfully");

        return factory;
    }
}
