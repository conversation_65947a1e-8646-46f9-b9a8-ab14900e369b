package com.concirrus.referencedataservice.service.impl;

import com.concirrus.referencedataservice.dal.ReferenceDataDAL;
import com.concirrus.referencedataservice.dto.request.EndorsementSearchCriteria;
import com.concirrus.referencedataservice.dto.request.FormSearchCriteria;
import com.concirrus.referencedataservice.model.exception.BadRequestException;
import com.concirrus.referencedataservice.service.ReferenceDataService;
import com.concirrus.referencedataservice.utils.LoggingUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.concirrus.referencedataservice.constant.CommonConstant.*;
import static com.concirrus.referencedataservice.constant.DBConstant.*;
import static com.concirrus.referencedataservice.constant.RequestConstant.REFERENCE_DATA;

/**
 * Service class for managing reference data operations, including CRUD and querying with dynamic filters.
 */
@Service
public class ReferenceDataServiceImpl implements ReferenceDataService {

    private static final Logger logger = LoggerFactory.getLogger(ReferenceDataServiceImpl.class);

    private final ReferenceDataDAL referenceDataDAL;

    @Autowired
    public ReferenceDataServiceImpl(ReferenceDataDAL referenceDataDAL) {
        this.referenceDataDAL = referenceDataDAL;
    }

    /**
     * Retrieves reference data from the data access layer based on the specified filters and pagination information.
     * <p>
     * This method logs the incoming filters and delegates the retrieval operation
     * to the data access layer (DAL). It returns a list of {@link Document}
     * objects that match the provided filter criteria and are optionally paginated.
     *
     * @param filters     a {@link Map} containing the filter criteria where each key
     *                    represents the field to filter by, and the corresponding value
     *                    is the value to match in the query.
     * @param pageRequest a {@link PageRequest} object that contains pagination
     *                    information (page number, page size). If null, no pagination
     *                    is applied.
     * @param clientId    the client identifier used to filter results. If null or empty,
     *                    this criterion is not applied.
     * @return a list of {@link Document} objects representing the reference data
     * that match the given filters. If no records are found, an empty list is returned.
     * @throws IllegalArgumentException if the provided filters are invalid or
     *                                  if any mandatory criteria are not met.
     */
    public Pair<Long, List<Document>> getReferenceData(Map<String, Object> filters, PageRequest pageRequest, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(filters, pageRequest, clientId));
        return referenceDataDAL.getReferenceData(filters, pageRequest, clientId);
    }

    /**
     * Retrieves a record from the data access layer based on the specified entity ID and client ID.
     * This method is annotated with {@link Cacheable}, enabling caching of the result based on
     * the combination of entity ID and client ID. If a record with the same entity ID and client ID
     * is requested again, the cached result will be returned, avoiding the need to query the database.
     *
     * @param entityId the unique identifier of the entity to be retrieved.
     *                 This must not be null or empty.
     * @param clientId the client identifier used to filter results. If null or empty,
     *                 the retrieval will be based solely on the entity ID.
     * @return the {@link Document} representing the retrieved entity. The result may be cached
     * for subsequent retrievals.
     * @throws IllegalArgumentException if the provided entityId is null or empty.
     */
    @Cacheable(value = REFERENCE_DATA, key = "#entityId + #clientId")
    public Document getRecordByEntityId(String entityId, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(entityId, clientId));
        return referenceDataDAL.getRecordByEntityId(entityId, clientId);
    }

    /**
     * Updates a record in the data access layer based on the specified entity ID and client ID.
     * <p>
     * This method first validates whether a record with the given entity ID exists by calling
     * {@link #getRecordByEntityId(String, String)}. If the record is found, it proceeds to
     * update the record with the provided updated data.
     * If no record exists with the given entity ID, an exception is thrown.
     *
     * @param entityId    the unique identifier of the entity to be updated.
     *                    This must not be null or empty.
     * @param updatedData a {@link Document} containing the fields to be updated.
     *                    This should not include the _id field, as it is immutable.
     * @param clientId    the client identifier used to ensure the correct document is updated.
     *                    If null or empty, the update may not be applied correctly.
     * @return the updated {@link Document} after the update operation, with the _id field
     * in hexadecimal string format.
     * @throws IllegalArgumentException if the provided entityId or updatedData is null or empty.
     * @throws RuntimeException         if the update operation fails due to a data access exception.
     */
    @CachePut(value = REFERENCE_DATA, key = "#entityId + #clientId")
    public Document updateRecordByEntityId(String entityId, Document updatedData, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(entityId));

        // validate record exists or not
        getRecordByEntityId(entityId, clientId);

        return referenceDataDAL.updateRecordByEntityId(entityId, updatedData, clientId);
    }

    /**
     * Adds a new record to the data access layer.
     * <p>
     * This method validates the input to ensure that the provided
     * {@link Document} representing the reference data entity is not null.
     * If valid, it proceeds to save the record by delegating the operation
     * to the data access layer (DAL).
     *
     * @param referenceDataEntity the {@link Document} containing the reference
     *                            data entity to be added. This must not be null.
     * @return the saved {@link Document} with any generated fields populated,
     * such as the _id field.
     * @throws BadRequestException if the provided referenceDataEntity is null.
     * @throws RuntimeException    if the save operation fails due to a
     *                             data access exception.
     */
    public Document addRecord(Document referenceDataEntity) {
        logger.info(LoggingUtils.logMethodEntry());
        if (referenceDataEntity == null) {
            throw new BadRequestException("Reference data entity cannot be null");
        }
        logger.info("Creating new record");
        return referenceDataDAL.save(referenceDataEntity);
    }

    /**
     * Deletes a record by its entity ID from the data access layer.
     * <p>
     * This method first validates the existence of the record by calling
     * {@link #getRecordByEntityId(String, String)}. If the record exists,
     * it proceeds to delete the record by delegating the operation to
     * the data access layer (DAL). This method also evicts the cached
     * reference data for the specified entity ID and client ID to ensure
     * that subsequent calls return the most up-to-date data.
     *
     * @param entityId the ID of the entity to be deleted. This must not be null or empty.
     * @param clientId the client ID associated with the entity. This must not be null or empty.
     * @throws RuntimeException if the delete operation fails due to a
     *                          data access exception.
     */
    @CacheEvict(value = REFERENCE_DATA, key = "#entityId + #clientId")
    public void deleteRecordById(String entityId, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(entityId, clientId));

        // validate record exists or not
        getRecordByEntityId(entityId, clientId);

        referenceDataDAL.deleteRecordById(entityId, clientId);
    }

    /**
     * Retrieves endorsements based on policy form, coverage type, state, pagination, and client ID.
     * <p>
     * Determines the endorsement type from the given policy form and coverage type, then fetches
     * matching endorsements. Endorsements are categorized as "Mandatory" or "Optional" based on
     * their state value and returned as a document.
     *
     * @param endorsementNature The nature of the endorsement (e.g., Mandatory, Optional). Can be null.
     * @param searchCriteria    {@link EndorsementSearchCriteria} containing filtering criteria such as
     *                          policy form, coverage type, state, and insured name.
     * @param pageRequest       {@link PageRequest} for pagination. Can be null.
     * @param clientId          The client ID associated with the request. Can be null or empty.
     * @return A {@link Pair} containing the total count and a list of endorsements as {@link Document} objects.
     * @throws BadRequestException If the coverage type or policy form is invalid.
     */
    @Override
    public Pair<Long, List<Document>> getEndorsementsBy(String endorsementNature, EndorsementSearchCriteria searchCriteria, PageRequest pageRequest, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(endorsementNature, searchCriteria, pageRequest, clientId));
        String endorsementType = determineEndorsementType(searchCriteria.getCoverageType(), searchCriteria.getPolicyForm());

        if (StringUtils.isBlank(endorsementType)) {
            String errorMessage = String.format("Invalid coverage type or policy form provided: coverageType='%s', policyForm='%s'", searchCriteria.getCoverageType(), searchCriteria.getPolicyForm());
            logger.error(errorMessage);
            throw new BadRequestException(errorMessage);
        }
        logger.info("Endorsement type determined: {}", endorsementType);

        boolean includeCorporateEndorsements = false;

        if (StringUtils.isNotBlank(searchCriteria.getInsuredName())) {
            // If insuredName includes the word "LLC" as a complete word, include Corporate Endorsement
            includeCorporateEndorsements = searchCriteria.getInsuredName().toUpperCase().matches(".*\\bLLC\\b.*");
        }

        // mongo query
        Pair<Long, List<Document>> totalCountAndEndorsements = referenceDataDAL.getEndorsementDataBy(endorsementNature, endorsementType, searchCriteria, includeCorporateEndorsements, pageRequest, clientId);

        List<Document> endorsements = totalCountAndEndorsements.getRight();

        if (StringUtils.equalsIgnoreCase(OPTIONAL, endorsementNature)) {
             endorsements = applyOptionalEndorsementSelection(totalCountAndEndorsements.getRight(), endorsementNature, searchCriteria, includeCorporateEndorsements);
        }

        logger.info("Fetched {} endorsements", endorsements.size());

        return Pair.of(totalCountAndEndorsements.getLeft(), endorsements);
    }

    @Override
    public Pair<Long, List<Document>> getEndorsementsListBy(String endorsementNature, String state, PageRequest pageRequest, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(endorsementNature, state, pageRequest, clientId));
        String carrierType = determineCarrierType(state);

        if (StringUtils.isBlank(carrierType)) {
            String errorMessage = String.format("Invalid state provided: insuredState='%s'", state);
            logger.error(errorMessage);
            throw new BadRequestException(errorMessage);
        }
        logger.info("Carrier type determined: {}", carrierType);

        // mongo query
        Pair<Long, List<Document>> totalCountAndEndorsements = referenceDataDAL.getEndorsementListDataBy(endorsementNature, carrierType, state, pageRequest, clientId);

        logger.info("Fetched {} endorsements", totalCountAndEndorsements.getRight().size());

        return Pair.of(totalCountAndEndorsements.getLeft(), totalCountAndEndorsements.getRight());
    }

    @Override
    public Pair<Long, List<Document>> getFormsListBy(String formNature, FormSearchCriteria criteria, PageRequest pageRequest, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(formNature, criteria, pageRequest, clientId));
        Pair<Long, List<Document>> totalCountAndEndorsements = referenceDataDAL.getFormsListDataBy(formNature, criteria.getClasses(), criteria.getLobAndConveyance(), criteria.getState(), pageRequest, clientId);
        logger.info("Fetched {} endorsements", totalCountAndEndorsements.getRight().size());
        return Pair.of(totalCountAndEndorsements.getLeft(), totalCountAndEndorsements.getRight());
    }

    /**
     * Determines the endorsement type based on the given coverage type and policy form.
     * <p>
     * This method evaluates the coverage type and policy form to return the corresponding
     * endorsement type. The mapping is as follows:
     * - If the coverage type is "EXCESS", the endorsement type is "XAHL".
     * - If the coverage type is "PRIMARY", it checks the policy form:
     * - If the policy form is "COMMERCIAL_AVIATION", the endorsement type is "CMHL".
     * - If the policy form is "CORPORATE_AIRCRAFT", the endorsement type is "CPHL".
     *
     * @param coverageType the type of coverage (e.g., "EXCESS", "PRIMARY"). Must not be null.
     * @param policyForm   the form of the policy (e.g., "COMMERCIAL_AVIATION", "CORPORATE_AIRCRAFT").
     *                     Must not be null.
     * @return the corresponding endorsement type as a String. Returns an empty string if no
     * matching endorsement type is found.
     */
    private String determineEndorsementType(String coverageType, String policyForm) {
        logger.info(LoggingUtils.logMethodEntry(policyForm, coverageType));

        if (StringUtils.equalsIgnoreCase(coverageType, EXCESS)) {
            return XAHL;
        }

        if (StringUtils.equalsIgnoreCase(coverageType, PRIMARY)) {
            if (StringUtils.equalsIgnoreCase(policyForm, COMMERCIAL_AIRCRAFT)) {
                return CMHL;
            }

            if (StringUtils.equalsIgnoreCase(policyForm, CORPORATE_AIRCRAFT)) {
                return CPHL;
            }
        }

        return StringUtils.EMPTY;
    }

    private String determineCarrierType(String state) {
        logger.info(LoggingUtils.logMethodEntry(state));
        if (StringUtils.equalsIgnoreCase(state, "CA")) {
            return "AAC";
        } else if (StringUtils.equalsIgnoreCase(state, "HI")) {
            return "CIC";
        } else if (StringUtils.equalsIgnoreCase(state, "NC")) {
            return "FCIC";
        } else if (StringUtils.equalsIgnoreCase(state, "CT") || StringUtils.equalsIgnoreCase(state, "MA") || StringUtils.equalsIgnoreCase(state, "ME") || StringUtils.equalsIgnoreCase(state, "MI") || StringUtils.equalsIgnoreCase(state, "OK") || StringUtils.equalsIgnoreCase(state, "WA")) {
            return "PIC";
        } else {
            return "CNI";
        }
    }


    /**
     * Applies optional endorsement flags to each endorsement document based on the given search criteria and nature.
     * <p>
     * If the endorsement nature is "Optional", the method evaluates each document to determine whether it should
     * be marked as "defaultSelected" based on the matching flags from the search criteria and whether
     * corporate endorsements are included.
     * </p>
     *
     * @param endorsements                  the list of endorsement documents to evaluate and modify
     * @param endorsementNature            the nature of the endorsement (e.g., "Optional")
     * @param searchCriteria               the criteria specifying which optional flags to include
     * @param includeCorporateEndorsements true if corporate-specific endorsements (like LLC) should be considered
     * @return the updated list of endorsement documents with the "defaultSelected" flag set accordingly
     */
    private List<Document> applyOptionalEndorsementSelection(List<Document> endorsements,
                                                             String endorsementNature,
                                                             EndorsementSearchCriteria searchCriteria,
                                                             boolean includeCorporateEndorsements) {
        logger.info(LoggingUtils.logMethodEntry(endorsementNature, searchCriteria, includeCorporateEndorsements));

        if (CollectionUtils.isEmpty(endorsements)) {
            return Collections.emptyList();
        }

        Set<String> activeFlags = new HashSet<>();

        if (searchCriteria.isIncludeWarEndorsements()) {
            activeFlags.add(WAR_HULL_OPTIONAL);
        }
        if (searchCriteria.isIncludeWarLiabilityEndorsements()) {
            activeFlags.add(WAR_LIABILITY_OPTIONAL);
        }
        if (searchCriteria.isIncludeTriaEndorsements()) {
            activeFlags.add(TRIA_HULL_OPTIONAL);
        }
        if (searchCriteria.isIncludeTrialLiabilityEndorsements()) {
            activeFlags.add(TRIA_LIABILITY_OPTIONAL);
        }
        if (searchCriteria.isIncludeLeadPolicyEndorsements()) {
            activeFlags.add(LEAD_POLICY_OPTIONAL);
        }
        if (includeCorporateEndorsements) {
            activeFlags.add(NAMED_INSURED_LLC_OPTIONAL);
        }

        if(CollectionUtils.isNotEmpty(activeFlags)) {
            for (Map<String, Object> document : endorsements) {
                boolean defaultSelect = activeFlags.stream()
                        .anyMatch(flag -> Boolean.TRUE.equals(document.get(flag)));

                document.put(DEFAULT_SELECTED, defaultSelect);
            }
        }

        return endorsements;
    }
}
