package com.concirrus.ReferenceDataService.service;

import com.concirrus.referencedataservice.dal.ReferenceDataDAL;
import com.concirrus.referencedataservice.model.exception.BadRequestException;
import com.concirrus.referencedataservice.service.impl.ReferenceDataServiceImpl;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static com.concirrus.referencedataservice.constant.DBConstant.CREATED_BY;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class ReferenceDataServiceImplTest {

    @Mock
    private ReferenceDataDAL referenceDataDAL;

    @InjectMocks
    private ReferenceDataServiceImpl referenceDataServiceImpl;

    private String clientId;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        clientId = "testClientId";
    }

    // getRecordByEntityId
    @Test
    void testGetRecordByEntityId1() {
        Document mockEntity = new Document();
        String entityId = "507f191e810c19729de860ea";
        when(referenceDataDAL.getRecordByEntityId(entityId, clientId)).thenReturn(mockEntity);

        Document result = referenceDataServiceImpl.getRecordByEntityId(entityId, clientId);

        assertEquals(mockEntity, result);
        verify(referenceDataDAL).getRecordByEntityId(entityId, clientId);
    }

    // getRecordByEntityId
    @Test
    void testGetRecordByEntityId3() {
        String entityId = "507f191e810c19729de860ea";
        Document mockEntity = new Document();
        when(referenceDataDAL.getRecordByEntityId(entityId, clientId)).thenReturn(mockEntity);

        Document result = referenceDataServiceImpl.getRecordByEntityId(entityId, clientId);
        assertNotNull(result);
        assertEquals(mockEntity, result);
        verify(referenceDataDAL).getRecordByEntityId(entityId, clientId);
    }

    // saveDataEntry
    @Test
    void testAddRecord1() {
        Document document = new Document();
        document.append(CREATED_BY, "New Data Entry");

        when(referenceDataDAL.save(any(Document.class)))
                .thenReturn(document);

        Document result = referenceDataServiceImpl.addRecord(document);

        verify(referenceDataDAL).save(eq(document));
        assertNotNull(result);
        assertEquals("New Data Entry", result.get(CREATED_BY)); // Check that the returned record contains the expected value
    }

    // saveDataEntry
    @Test
    void testAddRecord2() {
        Document document = null; // Invalid input

        Exception exception = assertThrows(BadRequestException.class, () -> {
            referenceDataServiceImpl.addRecord(document);
        });

        assertEquals("Reference data entity cannot be null", exception.getMessage());
    }

    // saveDataEntry
    @Test
    void testAddRecord3() {
        Document mockEntity = new Document();

        when(referenceDataDAL.save(any(Document.class)))
                .thenThrow(new RuntimeException("DAL Failure: Unable to save record"));

        Exception exception = assertThrows(RuntimeException.class, () -> {
            referenceDataServiceImpl.addRecord(mockEntity);
        });

        assertEquals("DAL Failure: Unable to save record", exception.getMessage());
    }

    // deleteRecordById
    @Test
    void testDeleteRecordById1() {
        String entityId = "entity123";

        referenceDataServiceImpl.deleteRecordById(entityId, clientId);

        verify(referenceDataDAL, times(1)).deleteRecordById(entityId, clientId);
    }


    // updateRecordByEntityId
    @Test
    void testUpdateRecordByEntityId4() {
        String entityId = "validEntityId";
        Document updatedData = new Document();

        when(referenceDataDAL.updateRecordByEntityId(eq(entityId), eq(updatedData), eq(clientId))).thenReturn(updatedData);

        Document result = referenceDataServiceImpl.updateRecordByEntityId(entityId, updatedData, clientId);

        verify(referenceDataDAL).updateRecordByEntityId(entityId, updatedData, clientId);
        assertNotNull(result);
        assertEquals(updatedData, result);
    }
}
