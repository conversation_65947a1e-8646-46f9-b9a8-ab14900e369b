package com.concirrus.referencedataservice.model.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serial;

/**
 * <AUTHOR> mohit
 * @since : Jan 2024
 */
@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "Database exception, Not able to fetch records")
public class DatabaseException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -3334521700736438531L;
    private static final String DEFAULT_MESSAGE = "Database exception, Not able to fetch records";

    public DatabaseException(String message) {
        super(message);
    }

    public DatabaseException() {
        super(DEFAULT_MESSAGE);
    }

    public DatabaseException(Throwable cause) {
        super(cause);
    }

}
