package com.concirrus.referencedataservice.dal.impl;

import com.concirrus.referencedataservice.dal.ReferenceDataDAL;
import com.concirrus.referencedataservice.dto.request.EndorsementSearchCriteria;
import com.concirrus.referencedataservice.model.exception.NoDataFoundException;
import com.concirrus.referencedataservice.utils.LoggingUtils;
import com.mongodb.client.model.FindOneAndUpdateOptions;
import com.mongodb.client.model.ReturnDocument;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.concirrus.referencedataservice.config.MongoConfig.MONGO_TEMPLATE;
import static com.concirrus.referencedataservice.constant.DBConstant.*;

@Repository
public class ReferenceDataDALImpl implements ReferenceDataDAL {

    private static final Logger logger = LoggerFactory.getLogger(ReferenceDataDALImpl.class);

    private final MongoTemplate mongoTemplate;

    /**
     * Constructs a new ReferenceDataDALImpl with the provided MongoTemplate.
     *
     * @param mongoTemplate the MongoTemplate used to interact with MongoDB.
     */
    @Autowired
    public ReferenceDataDALImpl(@Qualifier(MONGO_TEMPLATE) MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    /**
     * Retrieves endorsement documents based on various criteria, including endorsement type, state,
     * TRIA and War endorsements, and Corporate endorsements.
     *
     * @param endorsementNature          The nature of the endorsement (e.g., Mandatory, Optional). Can be null or empty.
     * @param endorsementType            The type of the endorsement to filter by. Can be null or empty.
     * @param searchCriteria             {@link EndorsementSearchCriteria} containing policy form, coverage type,
     *                                   state, and optional insured name.
     * @param includeCorporateEndorsements Whether to include Corporate endorsements (e.g., "Holding Corporation").
     * @param pageRequest                {@link PageRequest} for pagination. Can be null.
     * @param clientId                   The client ID to filter the endorsements by. Can be null or empty.
     * @return A {@link Pair} containing the total count and a list of matching {@link Document} objects.
     * @throws IllegalArgumentException If any invalid parameters are provided.
     */
    public Pair<Long, List<Document>> getEndorsementDataBy(
            String endorsementNature,
            String endorsementType,
            EndorsementSearchCriteria searchCriteria,
            boolean includeCorporateEndorsements,
            PageRequest pageRequest,
            String clientId) {

        logger.info(LoggingUtils.logMethodEntry(endorsementNature, endorsementType, searchCriteria, includeCorporateEndorsements, pageRequest, clientId));

        // Prepare the outer OR criteria
        List<Criteria> orCriteria = new ArrayList<>();

        // Adding Tria Endorsements as OR condition if applicable
        if (searchCriteria.isIncludeTriaEndorsements()) {
            // Define the regex with word boundary to match "TRIA" as a complete word
            String triaRegex = "\\btria\\b";
            orCriteria.add(Criteria.where(ENDORSEMENT_DATA_QUERY_KEY + FORM_NAME).regex(triaRegex, "i"));
        }

        // Adding War Endorsements as OR condition if applicable
        if (searchCriteria.isIncludeWarEndorsements()) {
            // Define the regex with word boundary to match "war" as a complete word
            String warRegex = "\\bwar\\b";
            orCriteria.add(Criteria.where(ENDORSEMENT_DATA_QUERY_KEY + FORM_NAME).regex(warRegex, "i"));
        }

        // Adding Corporate Endorsement as OR condition if applicable
        if (includeCorporateEndorsements) {
            // Define the regex with word boundary to match "Holding Corporation" as a complete phrase
            String corporateRegex = "\\bHolding Corporation\\b";
            orCriteria.add(Criteria.where(ENDORSEMENT_DATA_QUERY_KEY + FORM_NAME).regex(corporateRegex, "i"));
        }

        // Adding state criteria for "Mandatory" or "Optional" if not empty
        if (StringUtils.isNotEmpty(searchCriteria.getState())) {
            String value = StringUtils.equalsIgnoreCase(MANDATORY, endorsementNature) ? MANDATORY : OPTIONAL;
            orCriteria.add(Criteria.where(ENDORSEMENT_DATA_QUERY_KEY + searchCriteria.getState().trim()).is(value));
        }

        // Prepare the AND criteria for entityType, clientId, endorsementType, and state
        List<Criteria> andCriteria = new ArrayList<>();

        // Adding entity type criteria
        andCriteria.add(Criteria.where(ENTITY_TYPE).is(ENDORSEMENT));

        // Adding clientId criteria if not empty
        if (StringUtils.isNotEmpty(clientId)) {
            andCriteria.add(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        // Adding endorsementType criteria if not empty
        if (StringUtils.isNotEmpty(endorsementType)) {
            andCriteria.add(Criteria.where(ENDORSEMENT_TYPE).is(endorsementType));
        }

        // Adding issuingCompany criteria if not empty
        if (StringUtils.isNotEmpty(searchCriteria.getIssuingCompany())) {
            andCriteria.add(Criteria.where(ENDORSEMENT_DATA_QUERY_KEY + ISSUING_COMPANY)
                    .in(searchCriteria.getIssuingCompany(), "ALL"));

        }

        // Adding state criteria in and clause for "Mandatory" or "Optional" if not empty
        if (StringUtils.isNotEmpty(searchCriteria.getState())) {
            String value = StringUtils.equalsIgnoreCase(MANDATORY, endorsementNature) ? MANDATORY : OPTIONAL;
            andCriteria.add(Criteria.where(ENDORSEMENT_DATA_QUERY_KEY + searchCriteria.getState().trim()).is(value));
        }

        // Combine the OR criteria into the AND condition if there are any
        if (CollectionUtils.isNotEmpty(orCriteria)) {
            andCriteria.add(new Criteria().orOperator(orCriteria.toArray(new Criteria[0])));
        }

        // Create the final query
        Query query = new Query();
        query.addCriteria(new Criteria().andOperator(andCriteria.toArray(new Criteria[0])));

        // Total count of items that match the condition
        long totalCount = mongoTemplate.count(query, Document.class, REFERENCE_MONGO_COLLECTION);

        // Adding pagination to the query if pagination is provided
        if (Objects.nonNull(pageRequest)) {
            getPaginationQueryForMongo(query, pageRequest);
        }

        // Executing the query and returning results
        List<Document> endorsements = mongoTemplate.find(query, Document.class, REFERENCE_MONGO_COLLECTION);

        endorsements.forEach(doc -> {
            ObjectId objectId = doc.getObjectId(MONGO_ID);
            doc.put(MONGO_ID, objectId.toHexString());
        });

        return Pair.of(totalCount, endorsements);
    }

    public Pair<Long, List<Document>> getEndorsementListDataBy(String endorsementNature, String carrierType, String state, PageRequest pageRequest, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(endorsementNature, carrierType, state, pageRequest, clientId));

        Query query = new Query();
        if (StringUtils.isNotEmpty(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        if (StringUtils.isNotEmpty(carrierType)) {
            query.addCriteria(Criteria.where(ENDORSEMENT_DATA_QUERY_KEY + CARRIER_TYPE).is(carrierType));
        }

        // Adding state criteria for "Mandatory" or "Optional" if not empty
        if (StringUtils.isNotEmpty(state)) {
            String value = StringUtils.equalsIgnoreCase(MANDATORY, endorsementNature) ? MANDATORY : OPTIONAL;
            query.addCriteria(Criteria.where(ENDORSEMENT_DATA_QUERY_KEY + state.trim()).is(value));
        }

        // total count of items that matches the condition
        long totalCount = mongoTemplate.count(query, Document.class, REFERENCE_MONGO_COLLECTION);

        // Adding pagination to the query
        if (Objects.nonNull(pageRequest)) {
            getPaginationQueryForMongo(query, pageRequest);
        }

        List<Document> data = mongoTemplate.find(query, Document.class, REFERENCE_MONGO_COLLECTION);

        data.forEach(doc -> {
            ObjectId objectId = doc.getObjectId(MONGO_ID);
            doc.put(MONGO_ID, objectId.toHexString());
        });

        return Pair.of(totalCount, data);
    }

    @Override
    public Pair<Long, List<Document>> getFormsListDataBy(String formNature, List<String> classes, List<String> lobAndConveyance, String state, PageRequest pageRequest, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(formNature, classes, lobAndConveyance, state, pageRequest, clientId));
        Query query = new Query();

        if (StringUtils.isNotEmpty(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        if (StringUtils.isNotEmpty(state)) {
            String value = StringUtils.equalsIgnoreCase(MANDATORY, formNature) ? MANDATORY : OPTIONAL;
            query.addCriteria(Criteria.where(ENDORSEMENT_DATA_QUERY_KEY + state.trim()).is(value));
        }

        // Collect all criteria in a list for AND operation
        List<Criteria> allCriteria = new ArrayList<>();

        // Handle NT (Notice of Terrorism) mutual exclusion logic
        boolean isNTRequested = classes != null && classes.contains("NT");

        if (isNTRequested) {
            // If NT is requested, only return documents where NT=true
            allCriteria.add(Criteria.where("data.NT").is(true));

            // Check other non-NT classes for additional filtering (separate OR condition)
            List<String> nonNTClasses = classes.stream()
                    .filter(logisticClass -> !"NT".equals(logisticClass))
                    .collect(Collectors.toList());

            if (!nonNTClasses.isEmpty()) {
                List<Criteria> classOrCriteria = nonNTClasses.stream()
                        .map(logisticClass -> Criteria.where("data." + logisticClass).is(true))
                        .collect(Collectors.toList());
                allCriteria.add(new Criteria().orOperator(classOrCriteria.toArray(new Criteria[0])));
            }
        } else {
            // If NT is NOT requested, we don't exclude NT documents by default
            // Apply class filter for the requested classes OR include TRIA Exclusion when NT is not selected
            if (classes != null && !classes.isEmpty()) {
                List<Criteria> classOrCriteria = classes.stream()
                        .map(logisticClass -> Criteria.where("data." + logisticClass).is(true))
                        .collect(Collectors.toList());

                // Special case: Include TRIA Exclusion when NT is not selected
                classOrCriteria.add(Criteria.where("data.formName").regex(".*TRIA Exclusion.*", "i"));

                allCriteria.add(new Criteria().orOperator(classOrCriteria.toArray(new Criteria[0])));
            }
        }

        // LineOfBusiness filter - conditionSet must contain at least one of the lineOfBusinesses (separate OR condition)
        if (lobAndConveyance != null && !lobAndConveyance.isEmpty()) {
            List<Criteria> lobOrCriteria = lobAndConveyance.stream()
                    .map(lob -> Criteria.where("data.conditionSet").regex("\\b" + Pattern.quote(lob) + "\\b", "i")) // Case-insensitive regex with word boundaries
                    .collect(Collectors.toList());
            allCriteria.add(new Criteria().orOperator(lobOrCriteria.toArray(new Criteria[0])));
        }

        // Add all criteria as a single AND operation
        if (!allCriteria.isEmpty()) {
            query.addCriteria(new Criteria().andOperator(allCriteria.toArray(new Criteria[0])));
        }

        // Add entity type filter to ensure we're only getting forms
        query.addCriteria(Criteria.where("entityType").is("FORMS"));

        // Count total matching documents for pagination info
        long total = mongoTemplate.count(query, Document.class, REFERENCE_MONGO_COLLECTION);

        // Apply pagination
        if (pageRequest != null) {
            query.with(pageRequest);
        }

        // Execute query with pagination
        List<Document> documents = mongoTemplate.find(query, Document.class, REFERENCE_MONGO_COLLECTION);

        logger.info("Found {} documents matching criteria", documents.size());

        return Pair.of(total, documents);
    }


    /**
     * Retrieves reference data from the specified MongoDB collection based on the provided filters.
     * <p>
     * This method constructs a MongoDB query using the provided filters,
     * applies pagination if a {@link PageRequest} is provided, and
     * retrieves the matching documents from the database.
     * Additionally, it converts the MongoDB ObjectId to a hexadecimal string format
     * for each document in the result set.
     *
     * @param filters     a {@link Map} containing filter criteria where each key
     *                    represents the field to filter by and the corresponding value
     *                    is the value to match in the query.
     * @param pageRequest a {@link PageRequest} object that contains pagination information
     *                    (page number, page size). If null, no pagination is applied.
     * @param clientId    the client identifier used to filter results. If null or empty,
     *                    this criterion is not applied.
     * @return a list of {@link Document} objects representing the reference data
     * that match the given criteria. Each document's _id field is converted
     * to a hexadecimal string format.
     * @throws IllegalArgumentException if the provided filters are invalid or if any
     *                                  mandatory criteria are not met.
     */
    @Override
    public Pair<Long, List<Document>> getReferenceData(Map<String, Object> filters, PageRequest pageRequest, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(filters, pageRequest, clientId));

        Query query = new Query();
        if (StringUtils.isNotEmpty(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        filters.forEach((key, value) -> query.addCriteria(Criteria.where(key).is(value)));

        // total count of items that matches the condition
        long totalCount = mongoTemplate.count(query, Document.class, REFERENCE_MONGO_COLLECTION);

        // Adding pagination to the query
        if (Objects.nonNull(pageRequest)) {
            getPaginationQueryForMongo(query, pageRequest);
        }

        List<Document> data = mongoTemplate.find(query, Document.class, REFERENCE_MONGO_COLLECTION);

        data.forEach(doc -> {
            ObjectId objectId = doc.getObjectId(MONGO_ID);
            doc.put(MONGO_ID, objectId.toHexString());
        });

        return Pair.of(totalCount, data);
    }

    /**
     * Updates a record in the MongoDB collection based on the specified entity ID and client ID.
     * <p>
     * This method removes the immutable ID from the provided updated data, constructs a query
     * to find the document by its ID and client ID, and performs the update operation.
     * It then retrieves and returns the updated document. The MongoDB ObjectId is converted
     * to a hexadecimal string format in the returned document.
     *
     * @param entityId    the unique identifier of the entity to be updated.
     * @param updatedData a {@link Document} containing the fields to be updated.
     *                    This should not include the _id field, as it is immutable.
     * @param clientId    the client identifier used to ensure the correct document is updated.
     *                    If null or empty, the update may not be applied correctly.
     * @return the updated {@link Document} after the update operation, with the _id field
     * converted to a hexadecimal string format.
     * @throws IllegalArgumentException if the provided entityId or clientId is invalid.
     * @throws NullPointerException     if the updatedData is null or if no document matches
     *                                  the query criteria.
     */
    public Document updateRecordByEntityId(String entityId, Document updatedData, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(entityId, clientId));
        // Remove immutable ID from the update object
        updatedData.remove(MONGO_ID);

        // Create the query to match the document
        Document updateQuery = new Document(MONGO_ID, entityId)
                .append(MONGO_CLIENT_ID, clientId);

        // Create the update document
        Document updateDocument = new Document("$set", updatedData);

        // Perform the update operation and retrieve the updated document
        Document data = mongoTemplate.getCollection(REFERENCE_MONGO_COLLECTION)
                .findOneAndUpdate(updateQuery, updateDocument,
                        new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));

        ObjectId objectId = data.getObjectId(MONGO_ID);
        data.put(MONGO_ID, objectId.toHexString());

        return data;
    }

    /**
     * Saves a new reference data entity to the MongoDB collection.
     * <p>
     * This method attempts to create a new record by saving the provided
     * {@link Document} representing the reference data entity to the specified
     * MongoDB collection. After saving, it converts the MongoDB ObjectId to
     * a hexadecimal string format for the _id field in the returned document.
     *
     * @param referenceDataEntity the {@link Document} containing the reference
     *                            data entity to be saved. This should not
     *                            include the _id field, as it is automatically
     *                            generated by MongoDB.
     * @return the saved {@link Document} with the _id field converted to a
     * hexadecimal string format.
     * @throws RuntimeException if the save operation fails due to a
     *                          data access exception.
     */
    @Override
    public Document save(Document referenceDataEntity) {
        logger.info(LoggingUtils.logMethodEntry());
        try {
            logger.info("Creating new record");
            Document data = mongoTemplate.save(referenceDataEntity, REFERENCE_MONGO_COLLECTION);

            ObjectId objectId = data.getObjectId(MONGO_ID);
            data.put(MONGO_ID, objectId.toHexString());

            return data;

        } catch (DataAccessException e) {
            logger.error("Failed to save the record: {}", e.getMessage());
            throw new RuntimeException("DAL Failure: Unable to save record", e);
        }
    }

    /**
     * Deletes a record from the MongoDB collection based on the specified entity ID and client ID.
     * <p>
     * This method constructs a query to locate the document by its ID and client ID,
     * then performs the delete operation. If the document is found and successfully deleted,
     * a log message is generated to confirm the operation.
     *
     * @param entityId the unique identifier of the entity to be deleted.
     * @param clientId the client identifier used to ensure the correct document is deleted.
     *                 If null or empty, the deletion will be based solely on the entity ID.
     * @throws IllegalArgumentException if the provided entityId is null or empty.
     * @throws RuntimeException         if the delete operation fails due to a
     *                                  data access exception.
     */
    @Override
    public void deleteRecordById(String entityId, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(entityId, clientId));
        Query query = new Query();
        query.addCriteria(Criteria.where(MONGO_ID).is(entityId));

        if (StringUtils.isNotEmpty(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        mongoTemplate.remove(query, REFERENCE_MONGO_COLLECTION);

        logger.info("Record deleted successfully for entityId: {}", entityId);
        logger.info(LoggingUtils.logMethodExit());
    }

    /**
     * Creates a MongoDB Query object for pagination based on the provided PageRequest.
     *
     * @param req The PageRequest containing pagination details.
     * @return A Query object with skip, limit, and sort parameters for MongoDB pagination.
     */
    public static Query getPaginationQueryForMongo(Query query, PageRequest req) {
        final int limit = req.getPageSize();
        final int offset = req.getPageNumber() * req.getPageSize();

        Sort sortOrder = req.getSort();

        if (Objects.isNull(query)) {
            query = new Query();
        }
        query.skip(offset).limit(limit);
        query.with(sortOrder);

        logger.info("The pagination query is limit:{}, offset:{}, sort:{}", query.getLimit(), query.getSkip(), query.getSortObject());
        return query;
    }

    /**
     * Retrieves a record from the MongoDB collection based on the specified entity ID and client ID.
     * <p>
     * This method constructs a query to locate the document by its unique entity ID
     * and optionally by client ID. If the document is found, it is returned as a
     * {@link Document} object. The MongoDB ObjectId is converted to a hexadecimal string
     * format for the _id field in the returned document. If no document is found,
     * a {@link NoDataFoundException} is thrown.
     *
     * @param entityId the unique identifier of the entity to be retrieved.
     *                 This must not be null or empty.
     * @param clientId the client identifier used to filter results. If null or empty,
     *                 the retrieval will be based solely on the entity ID.
     * @return the {@link Document} representing the retrieved entity. The _id field
     * is converted to a hexadecimal string format.
     * @throws NoDataFoundException     if no document matches the provided entity ID and
     *                                  client ID.
     * @throws IllegalArgumentException if the provided entityId is null or empty.
     */
    public Document getRecordByEntityId(String entityId, String clientId) {
        logger.info(LoggingUtils.logMethodEntry(entityId, clientId));

        Query query = new Query();
        if (StringUtils.isNotEmpty(entityId)) {
            query.addCriteria(Criteria.where(MONGO_ID).is(entityId));
        }

        if (StringUtils.isNotEmpty(clientId)) {
            query.addCriteria(Criteria.where(MONGO_CLIENT_ID).is(clientId));
        }

        Document existingEntity = mongoTemplate.findOne(query, Document.class, REFERENCE_MONGO_COLLECTION);

        if (existingEntity == null) {
            throw new NoDataFoundException("Entity not found for ID: " + entityId);
        }

        ObjectId objectId = existingEntity.getObjectId(MONGO_ID);
        existingEntity.put(MONGO_ID, objectId.toHexString());

        return existingEntity;
    }
}
