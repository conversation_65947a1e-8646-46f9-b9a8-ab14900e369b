package com.concirrus.referencedataservice.model.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception thrown when a user is not authorized to access a resource.
 *
 * <p>This exception is mapped to HTTP status code 401 Unauthorized.</p>
 */
@ResponseStatus(value= HttpStatus.UNAUTHORIZED, reason="Unauthorized Access.")
public class UnAuthorisedException extends RuntimeException {

    /**
     * Constructs a new UnauthorizedException with a default message.
     */
    public UnAuthorisedException() {
        super("Unauthorized Access.");
    }

    /**
     * Constructs a new UnauthorizedException with a specified message.
     *
     * @param message the detail message
     */
    public UnAuthorisedException(String message) {
        super(message);
    }
}
