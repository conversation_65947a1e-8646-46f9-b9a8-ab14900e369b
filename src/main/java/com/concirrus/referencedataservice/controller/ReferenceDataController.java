package com.concirrus.referencedataservice.controller;

import com.concirrus.referencedataservice.dto.response.PagedResponse;
import com.concirrus.referencedataservice.service.ReferenceDataService;
import com.concirrus.referencedataservice.service.impl.ReferenceDataServiceImpl;
import com.concirrus.referencedataservice.utils.LoggingUtils;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.concirrus.referencedataservice.constant.DBConstant.CREATED_AT;
import static com.concirrus.referencedataservice.constant.DBConstant.MONGO_ID;
import static com.concirrus.referencedataservice.constant.RequestConstant.*;

/**
 * Controller for managing reference data entities.
 * <p>
 * This class handles HTTP requests for creating, reading, updating, and deleting
 * reference data entities in the system. It interacts with the {@link ReferenceDataServiceImpl}
 * to perform operations on the underlying data store.
 * </p>
 */
@RestController
@RequestMapping("/entities")
public class ReferenceDataController {

    private static final Logger logger = LoggerFactory.getLogger(ReferenceDataController.class);

    private final ReferenceDataService referenceDataService;

    @Autowired
    public ReferenceDataController(ReferenceDataService referenceDataService) {
        this.referenceDataService = referenceDataService;
    }

    /**
     * Retrieves a reference data entry by its entity ID.
     *
     * @param entityId the ID of the reference data entity to retrieve.
     * @return a ResponseEntity containing the found reference data entity.
     */
    @GetMapping("/{entityId}")
    public PagedResponse getRecordByEntityId(@PathVariable String entityId,
                                             @RequestHeader(value = CLIENT_ID) String clientId) {
        logger.info(LoggingUtils.logMethodEntry(entityId, clientId));
        PagedResponse response = new PagedResponse();
        response.setData(referenceDataService.getRecordByEntityId(entityId, clientId));
        return response;
    }

    /**
     * Updates an existing reference data entry.
     *
     * @param entityId            the ID of the reference data entity to update.
     * @param referenceDataEntity the updated reference data entity.
     * @return a ResponseEntity containing the updated reference data entity.
     */
    @PutMapping("/{entityId}")
    public PagedResponse updateRecordByEntityId(
            @PathVariable String entityId,
            @RequestBody Document referenceDataEntity,
            @RequestHeader(value = CLIENT_ID) String clientId) {
        logger.info(LoggingUtils.logMethodEntry(entityId, clientId));

        PagedResponse response = new PagedResponse();
        response.setData( referenceDataService.updateRecordByEntityId(entityId, referenceDataEntity, clientId));
        return response;
    }

    /**
     * Creates a new reference data entry.
     *
     * @param referenceDataEntity the reference data entity to be created.
     * @return a ResponseEntity containing the created reference data entity.
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public PagedResponse createReferenceData(@RequestBody Document referenceDataEntity) {
        logger.info("Creating new record");
        Document savedEntity = referenceDataService.addRecord(referenceDataEntity);
        logger.info("Record created successfully with entityId: {}", savedEntity.get(MONGO_ID));

        PagedResponse response = new PagedResponse();
        response.setData( savedEntity);
        return response;
    }

    /**
     * Handles HTTP POST requests to fetch reference data based on the provided request body,
     * pagination parameters, and client ID.
     * <p>
     * This method processes incoming requests, extracting filters and pagination settings from the
     * request body and parameters. It then calls the service layer to retrieve a list of reference
     * data entities that match the provided criteria. The client ID is mandatory and is used to
     * filter the results according to the requesting client.
     *
     * @param filters    A map containing the filters and criteria for fetching reference data. Each
     *                   key-value pair in the map represents a filter to be applied to the search.
     * @param pageNumber The page number for pagination. Defaults to 1 if not provided. Must be greater than 0.
     * @param pageSize   The number of records to return per page. Defaults to 10 if not provided. Must be greater than 0.
     * @param clientId   The mandatory ID of the client requesting the reference data. This header must be provided
     *                   in the request and is used to filter results specific to the client.
     * @return A ResponseEntity containing a list of objects that match the
     * provided filters and pagination criteria. If an error occurs, returns a bad request response
     * with an empty list.
     * @throws IllegalArgumentException if the provided page number or page size is less than or equal to 0.
     */
    @PostMapping("/search")
    public PagedResponse getReferenceData(@RequestBody Map<String, Object> filters,
                                                           @RequestParam(defaultValue = ZERO) @Min(PAGE_NUMBER_MIN) int pageNumber,
                                                           @RequestParam(defaultValue = TWENTY) @Min(PAGE_SIZE_MIN) @Max(PAGE_SIZE_MAX) int pageSize,
                                                           @RequestParam(defaultValue = ASC) Sort.Direction sortOrder,
                                                           @RequestParam(defaultValue = CREATED_AT) String sortBy,
                                                           @RequestHeader(value = CLIENT_ID) String clientId) {
        logger.info(LoggingUtils.logMethodEntry(filters, pageNumber, pageSize, clientId));
        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortOrder, sortBy));
        Pair<Long, List<Document>> referenceData = referenceDataService.getReferenceData(filters, pageRequest, clientId);
        logger.info("Fetched {} references", referenceData.getRight().size());

        PagedResponse response = new PagedResponse();
        response.setData(referenceData.getRight());
        response.setTotalItems(referenceData.getLeft());
        return response;
    }

    /**
     * Deletes a record by its entityId.
     *
     * @param entityId the ID of the entity to delete.
     * @return a ResponseEntity indicating the result of the operation.
     */
    @DeleteMapping("/{entityId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public PagedResponse deleteReferenceData(@PathVariable String entityId,
                                                      @RequestHeader(value = CLIENT_ID) String clientId) {
        logger.info(LoggingUtils.logMethodEntry(entityId, clientId));
        referenceDataService.deleteRecordById(entityId, clientId);
        logger.info("Deleted record with entityId: {}", entityId);

        PagedResponse response = new PagedResponse();
        response.setData("Record deleted successfully.");
        return response;
    }

    /**
     * Clears the cache for the application.
     *
     * @return A response indicating cache clearance.
     */
    @DeleteMapping("/cache")
    @CacheEvict(value = REFERENCE_DATA, allEntries = true)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ResponseEntity<Void> clearCache() {
        logger.info(LoggingUtils.logMethodEntry());
        logger.info("Cache cleared successfully");
        return ResponseEntity.noContent().build();
    }
}