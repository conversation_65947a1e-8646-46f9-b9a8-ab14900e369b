package com.concirrus.referencedataservice.controller;

import com.concirrus.referencedataservice.dto.request.EndorsementSearchCriteria;
import com.concirrus.referencedataservice.dto.response.PagedResponse;
import com.concirrus.referencedataservice.service.ReferenceDataService;
import com.concirrus.referencedataservice.service.impl.ReferenceDataServiceImpl;
import com.concirrus.referencedataservice.utils.LoggingUtils;
import jakarta.validation.constraints.Min;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.concirrus.referencedataservice.constant.DBConstant.CREATED_AT;
import static com.concirrus.referencedataservice.constant.RequestConstant.*;

/**
 * Controller for managing reference data entities.
 * <p>
 * This class handles HTTP requests for creating, reading, updating, and deleting
 * reference data entities in the system. It interacts with the {@link ReferenceDataServiceImpl}
 * to perform operations on the underlying data store.
 * </p>
 */
@RestController
@RequestMapping("/endorsements")
public class EndorsementDataController {

    private static final Logger logger = LoggerFactory.getLogger(EndorsementDataController.class);

    private final ReferenceDataService referenceDataService;

    @Autowired
    public EndorsementDataController(ReferenceDataService referenceDataService) {
        this.referenceDataService = referenceDataService;
    }


    @GetMapping("/{endorsementNature}")
    public PagedResponse getEndorsementBy(
            @PathVariable String endorsementNature,
            @ModelAttribute EndorsementSearchCriteria criteria,
            @RequestParam(defaultValue = ZERO) @Min(PAGE_NUMBER_MIN) int pageNumber,
            @RequestParam(defaultValue = TEN) @Min(PAGE_SIZE_MIN) int pageSize,
            @RequestParam(defaultValue = ASC) Sort.Direction sortOrder,
            @RequestParam(defaultValue = CREATED_AT) String sortBy,
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestHeader(value = LINE_OF_BUSINESS) String lob) {
        logger.info(LoggingUtils.logMethodEntry(endorsementNature, criteria, pageNumber, pageSize, sortOrder, sortBy, clientId, lob));
        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortOrder, sortBy));
        PagedResponse response = new PagedResponse();
        Pair<Long, List<Document>> data = APPLIED_LOGISTICS.equalsIgnoreCase(lob)
                ? referenceDataService.getEndorsementsListBy(endorsementNature, criteria.getState(), pageRequest, clientId)
                : referenceDataService.getEndorsementsBy(endorsementNature, criteria, pageRequest, clientId);
        response.setData(data.getRight());
        response.setTotalItems(data.getLeft());
        return response;
    }

    @PostMapping("/{endorsementNature}/search")
    public PagedResponse getEndorsementByFilter(
            @PathVariable String endorsementNature,
            @RequestBody EndorsementSearchCriteria criteria,
            @RequestParam(defaultValue = ZERO) @Min(PAGE_NUMBER_MIN) int pageNumber,
            @RequestParam(defaultValue = TEN) @Min(PAGE_SIZE_MIN) int pageSize,
            @RequestParam(defaultValue = ASC) Sort.Direction sortOrder,
            @RequestParam(defaultValue = CREATED_AT) String sortBy,
            @RequestHeader(value = CLIENT_ID) String clientId) {
        logger.info(LoggingUtils.logMethodEntry(endorsementNature, criteria, pageNumber, pageSize, sortOrder, sortBy, clientId));
        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortOrder, sortBy));
        PagedResponse response = new PagedResponse();
        Pair<Long, List<Document>> data = referenceDataService.getEndorsementsBy(endorsementNature, criteria, pageRequest, clientId);
        response.setData(data.getRight());
        response.setTotalItems(data.getLeft());
        return response;
    }
}