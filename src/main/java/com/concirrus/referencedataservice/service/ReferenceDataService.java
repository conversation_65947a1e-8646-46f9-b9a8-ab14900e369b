package com.concirrus.referencedataservice.service;

import com.concirrus.referencedataservice.dto.request.EndorsementSearchCriteria;
import com.concirrus.referencedataservice.dto.request.FormSearchCriteria;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Map;

public interface ReferenceDataService {
    Pair<Long, List<Document>> getReferenceData(Map<String, Object> filters, PageRequest pageRequest, String clientId);

    Document getRecordByEntityId(String entityId, String clientId);

    Document updateRecordByEntityId(String entityId, Document updatedData, String clientId);

    Document addRecord(Document referenceDataEntity);

    void deleteRecordById(String entityId, String clientId);

    Pair<Long, List<Document>> getEndorsementsBy(String endorsementNature, EndorsementSearchCriteria searchCriteria, PageRequest pageRequest, String clientId);

    Pair<Long, List<Document>> getEndorsementsListBy(String endorsementNature, String state, PageRequest pageRequest, String clientId);

    Pair<Long, List<Document>> getFormsListBy(String formNature, FormSearchCriteria criteria, PageRequest pageRequest, String clientId);
}
